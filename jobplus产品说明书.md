
### **JobPlus 产品需求文档 (PRD)**

*   **产品名称：** JobPlus
*   **文档版本：** V1.0 (终版)
*   **更新日期：** 2023年10月27日
*   **文档作者：** [你的资深产品经理]
*   **文档状态：** 最终版

---

### **1. 产品概述**

#### **1.1 产品愿景**
利用AI技术，打造一款温暖、智能的面试辅助软件，赋能每一位在求职路上遇到困难的用户，帮助他们挖掘自身亮点、增强表达自信、弥补知识短板，最终成功获得心仪的Offer。

#### **1.2 目标用户**
主要面向在求职面试中遇到以下困难的C端用户：
*   **“茶壶煮饺子”型：** 具备技术实力或业务能力，但面试时紧张、不善言辞，无法清晰地表达自己的项目经历和成果。
*   **“知识零散”型：** 掌握的知识点不成体系，面试时对“八股文”或基础概念掌握不牢，需要系统性梳理和练习。
*   **“缺乏经验”型：** 应届生或转行者，缺少实战面试经验，希望有安全、无压力的环境进行高仿真模拟。

#### **1.3 核心价值主张**
JobPlus 不仅仅是一个面试工具，更是用户的**“个人AI面试教练”**和**“可进化的第二大脑”**。它通过深度结合用户的简历和个人知识，提供从简历优化、模拟练习、实时辅助到复盘学习的全链路服务，让AI的帮助充满个人印记。

---

### **2. 产品范围与版本规划**

为了确保单人开发的敏捷性和高效率，我们将产品开发分为三个主要阶段：

| 版本 | 核心目标 | 主要功能模块 |
| :--- | :--- | :--- |
| **V1.0 (MVP)** | **验证核心闭环：** 用户愿意使用AI进行面试练习，并感知到价值。 | 账户系统、简历中心（上传/解析/STAR优化）、AI模拟面试、基础面试记录。 |
| **V1.1 (迭代)** | **构建学习闭环，上线杀手级应用：** 提升用户粘性，形成竞争壁垒。 | 实时面试辅助、AI复盘报告、公共知识库（含智能跳转）。 |
| **V2.0 (深化)** | **打造终极个性化体验，构建产品护城河：** 让产品成为用户不可或缺的个人资产。 | 个人知识库（向量化）、RAG智能应用、简历JD匹配、社交登录。 |

---

### **3. 详细功能需求 (Feature Requirements)**

#### **模块一：用户账户与认证**

*   **3.1.1 用户注册 (V1.0)**
    *   **用户故事：** 作为一个新用户，我希望通过邮箱和密码快速注册，以便开始使用产品。
    *   **功能描述：**
        1.  用户输入`用户名`、`邮箱`、`密码`。
        2.  前端进行基础格式校验（如邮箱格式、密码长度）。
        3.  后端发送`验证码`至用户邮箱。
        4.  用户输入验证码完成注册。
    *   **技术要求：** **密码必须使用加盐哈希（如BCrypt）加密存储。**

*   **3.1.2 用户登录 (V1.0)**
    *   **功能描述：** 支持`用户名/邮箱` + `密码`登录。提供“记住我”选项。

*   **3.1.3 找回密码 (V1.0)**
    *   **功能描述：** 用户通过输入注册邮箱，接收密码重置链接邮件，点击链接跳转至重置页面设置新密码。

*   **3.1.4 社交登录 (V2.0)**
    *   **功能描述：** 支持用户通过GitHub或LinkedIn账户一键授权登录。

#### **模块二：Dashboard (个人仪表盘)**

*   **3.2.1 核心引导与信息展示 (V1.0)**
    *   **功能描述：**
        1.  页面最醒目位置提供一个清晰的行动号召（CTA）按钮：**“开始一场模拟面试”**。
        2.  简洁展示用户头像/昵称、总使用时长、剩余可用时长等基础信息。
        3.  以卡片列表形式展示最近3-5次的面试记录，点击可跳转至记录详情。

*   **3.2.2 激励化与新手引导 (V1.1)**
    *   **功能描述：**
        1.  引入游戏化元素，如“本周练习时长已超过80%的用户”、“连续练习5天”等激励性提示。
        2.  为首次登录的用户提供一个简短的任务式引导，指引其完成“上传简历 -> 模拟面试”的核心流程。

#### **模块三：简历中心 (核心数据基础)**

*   **3.3.1 简历上传与AI解析 (V1.0)**
    *   **用户故事：** 作为一个用户，我希望能直接上传我的简历PDF，让AI自动帮我整理成结构化信息，为后续的面试做准备。
    *   **功能描述：**
        1.  用户点击上传按钮，选择本地的 `.pdf` 或 `.docx` 格式简历文件。
        2.  后端调用大模型（如Gemini）的文档理解与信息提取能力，将简历内容解析为结构化数据。
        3.  解析出的字段包括：`个人信息`、`教育背景`、`工作经历`、`项目经历`、`技能栈`。
        4.  在页面上以清晰、可编辑的表单形式展示解析结果。

*   **3.3.2 AI简历优化 - STAR原则助手 (V1.0)**
    *   **功能描述：**
        1.  在每一条“项目/工作经历”的编辑区旁，提供一个“AI优化”按钮。
        2.  点击后，AI会引导用户分别输入或补充Situation (情境), Task (任务), Action (行动), Result (结果)。
        3.  用户输入后，AI一键生成一段符合STAR原则、更具吸引力和专业性的描述，用户可直接采纳或修改后使用。

*   **3.3.3 AI简历优化 - JD匹配度分析 (V2.0)**
    *   **功能描述：**
        1.  提供一个文本框，让用户粘贴目标岗位的职位描述（JD）。
        2.  AI分析JD中的关键词（如技能、职责、经验要求），并与用户的结构化简历进行比对。
        3.  可视化地展示匹配度分数、简历中的优势关键词、以及JD中要求但简历缺失的关键词，并给出优化建议。

#### **模块四：面试功能 (核心场景)**

*   **3.4.1 AI模拟面试 (V1.0)**
    *   **用户故事：** 作为一个求职者，我希望有一个AI面试官，能根据我的简历，像真人一样向我提问，让我在一个安全的环境里反复练习。
    *   **功能描述：**
        1.  用户发起模拟面试，可选择面试的岗位方向（如Java后端）。
        2.  AI扮演面试官，首先会说：“你好，请先做个自我介绍吧。”
        3.  AI会根据用户的**结构化简历内容**，智能地提出连贯的问题，如：“我看到你简历里提到了‘凤凰’项目，能详细介绍一下吗？”、“你在其中主要负责什么？”
        4.  AI会穿插考察该岗位方向的通用技术问题（八股文）。
        5.  用户可通过`文字`输入进行回答。
        6.  整个对话流程以即时通讯（IM）的形式呈现。

*   **3.4.2 实时面试辅助 (V1.1)**
    *   **功能描述：**
        1.  用户在真实面试前开启此模式。
        2.  **问题捕获：** 桌面端程序（如C++或Python）捕获系统扬声器输出（即面试官的声音），进行实时语音转文字（STT）。
        3.  **智能作答：** 后端接收到问题文本后，结合用户的**结构化简历摘要**，通过Prompt工程，请求大模型生成回答建议。
        4.  **答案呈现：** 答案以两种形式在悬浮窗上呈现：
            *   **核心要点 (Bullet Points)：** 3-5个关键词或短句，供用户快速扫视。
            *   **参考回答：** 一段以第一人称“我”表述的、口语化的完整回答。
        5.  提供`自动/手动`作答模式切换。

#### **模块五：面试记录与复盘 (成长闭环)**

*   **3.5.1 基础面试记录 (V1.0)**
    *   **功能描述：** 自动保存每一次“模拟面试”的完整对话记录，按时间倒序排列，用户可随时查看。

*   **3.5.2 AI生成面试复盘报告 (V1.1)**
    *   **用户故事：** 作为一个希望进步的用户，我希望每次练习后都能得到一份专业的分析报告，告诉我哪里做得好，哪里需要改进，以及该学些什么。
    *   **功能描述：**
        1.  每次面试结束后，AI自动分析该次对话记录。
        2.  生成一份包含以下内容的可视化报告：
            *   `面试表现总览`：综合评分、流利度、关键词覆盖率等。
            *   `回答亮点`：摘录回答得好的部分。
            *   `待改进项`：分析回答模糊、逻辑不清或错误的地方。
            *   `知识盲区诊断`：根据回答情况，总结出用户可能掌握不牢的知识点。
            *   **智能跳转：** 报告中提到的知识盲区，可一键点击跳转至**公共知识库**中对应的学习模块。

#### **模块六：公共知识库 (学习中心)**

*   **3.6.1 预置内容与浏览搜索 (V1.1)**
    *   **功能描述：**
        1.  系统预置高质量的面试题库，初期聚焦`Java后端`、`前端`等1-2个热门岗位。
        2.  内容按`岗位 -> 知识领域 -> 具体知识点`的树状结构组织。
        3.  支持关键词全局搜索。
        4.  提供“隐藏/显示答案”的练习模式。

#### **模块七：个人知识库 (My AI Brain - 终极功能)**

*   **3.7.1 知识录入与管理 (V2.0)**
    *   **用户故事：** 作为一个追求极致面试效果的用户，我希望把我所有的项目细节、个人思考都“喂”给AI，让它在回答时能用上我独一无二的真实素材。
    *   **功能描述：**
        1.  提供简洁的界面，允许用户通过三种方式录入个人知识：
            *   `自由文本`：粘贴项目文档、技术笔记、博客文章等。
            *   `结构化Q&A`：添加自定义的“问题-答案”对。
            *   `文档上传`：支持 `.txt`, `.md` 格式的文档批量导入。
        2.  用户可以对自己录入的知识进行增、删、改、查。

*   **3.7.2 后台向量化与RAG应用 (V2.0)**
    *   **技术要求：**
        1.  **后台处理：** 用户录入的文本被自动切块(Chunking)，通过Embedding模型向量化，并与原文一同存入用户专属的**向量数据库**（如ChromaDB, Faiss）。
        2.  **RAG (检索增强生成) 应用：**
            *   在“AI模拟面试”或“实时面试辅助”中，当接收到面试官问题后，系统会：
                a. 将问题向量化，去用户的**个人知识库**中检索最相关的知识片段。
                b. 将`面试官问题` + `用户简历摘要` + `检索到的个人知识片段`三者结合，构建一个丰富的Prompt。
                c. 请求大模型基于此Prompt生成一个高度个性化、有血有肉的回答。
    *   **验收标准：** AI生成的回答，能明显体现出用户在个人知识库中录入的独特项目细节或个人观点。

---

### **4. 非功能性需求**

*   **4.1 性能 (Performance):**
    *   “实时面试辅助”模式下，从接收到问题到呈现答案的总延迟应控制在3秒以内。
    *   常规页面加载时间应小于2秒。

*   **4.2 安全 (Security):**
    *   用户密码必须加密存储。
    *   用户的简历、个人知识库等数据必须做到严格的租户隔离，确保数据隐私。
    *   所有与AI模型的API交互需走后端服务器，避免在前端暴露API Key。

*   **4.3 可用性 (Usability):**
    *   UI/UX设计应简洁、直观，学习成本低。
    *   关键功能（如实时辅助）应有清晰的使用引导。

---

这份文档是我们共同智慧的结晶，也是JobPlus从0到1，再到卓越的行动纲领。期待你用代码将它变为现实！在开发过程中有任何疑问，我们随时沟通。

