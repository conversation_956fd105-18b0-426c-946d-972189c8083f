{"version": 3, "file": "header.js", "sourceRoot": "", "sources": ["../../src/header.ts"], "names": [], "mappings": ";AAAA,gEAAgE;AAChE,oEAAoE;AACpE,+DAA+D;AAC/D,gEAAgE;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhE,yCAA+C;AAC/C,0DAA2C;AAE3C,kDAAmC;AA4BnC,MAAa,MAAM;IACjB,UAAU,GAAY,KAAK,CAAA;IAC3B,OAAO,GAAY,KAAK,CAAA;IACxB,SAAS,GAAY,KAAK,CAAA;IAE1B,KAAK,CAAS;IACd,IAAI,CAAS;IACb,IAAI,CAAS;IACb,GAAG,CAAS;IACZ,GAAG,CAAS;IACZ,IAAI,CAAS;IACb,KAAK,CAAS;IACd,KAAK,GAAkC,aAAa,CAAA;IACpD,QAAQ,CAAS;IACjB,KAAK,CAAS;IACd,KAAK,CAAS;IACd,MAAM,GAAW,CAAC,CAAA;IAClB,MAAM,GAAW,CAAC,CAAA;IAClB,KAAK,CAAO;IACZ,KAAK,CAAO;IACZ,KAAK,CAAO;IAEZ,OAAO,CAAS;IAChB,OAAO,CAAS;IAEhB,YACE,IAA0B,EAC1B,MAAc,CAAC,EACf,EAAe,EACf,GAAgB;QAEhB,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,CAAA;QACtC,CAAC;aAAM,IAAI,IAAI,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACnB,CAAC;IACH,CAAC;IAED,MAAM,CACJ,GAAW,EACX,GAAW,EACX,EAAe,EACf,GAAgB;QAEhB,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,GAAG,GAAG,CAAC,CAAA;QACT,CAAC;QAED,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;QAC9C,CAAC;QAED,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;QACpC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAA;QACxC,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAA;QACvC,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAA;QACvC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,CAAA;QACzC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,CAAA;QACxC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,CAAA;QAE1C,iEAAiE;QACjE,+CAA+C;QAC/C,6CAA6C;QAC7C,IAAI,GAAG;YAAE,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;QAC/B,IAAI,EAAE;YAAE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;QAEvB,2DAA2D;QAC3D,MAAM,CAAC,GAAG,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAA;QACtC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;YACpB,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,GAAG,CAAA;QACvB,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YACtD,IAAI,CAAC,KAAK,GAAG,GAAG,CAAA;QAClB,CAAC;QAED,mEAAmE;QACnE,gEAAgE;QAChE,kEAAkE;QAClE,qEAAqE;QACrE,kEAAkE;QAClE,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG,EAAE,CAAC;YACvB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;QACf,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAA;QAC9C,IACE,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE;YAC7C,eAAe,EACf,CAAC;YACD,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,CAAA;YAC1C,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,CAAA;YAC1C,qBAAqB;YACrB,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,CAAA;YAC/C,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,CAAA;YAC/C,oBAAoB;YACpB,IAAI,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzB,8CAA8C;gBAC9C,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAA;gBAC7C,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAA;YACtC,CAAC;iBAAM,CAAC;gBACN,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAA;gBAC7C,IAAI,MAAM,EAAE,CAAC;oBACX,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAA;gBACtC,CAAC;gBACD,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,CAAA;gBACxC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,CAAA;YAC1C,CAAC;QACH,CAAC;QAED,IAAI,GAAG,GAAG,CAAC,GAAG,IAAI,CAAA;QAClB,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;YACrC,GAAG,IAAI,GAAG,CAAC,CAAC,CAAW,CAAA;QACzB,CAAC;QAED,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3C,GAAG,IAAI,GAAG,CAAC,CAAC,CAAW,CAAA;QACzB,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,GAAG,KAAK,IAAI,CAAC,KAAK,CAAA;QACpC,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,EAAE,CAAC;YACjD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;QACvB,CAAC;IACH,CAAC;IAED,MAAM,CAAC,EAAc,EAAE,MAAe,KAAK;QACzC,MAAM,CAAC,MAAM,CACX,IAAI,EACJ,MAAM,CAAC,WAAW,CAChB,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;YACnC,0DAA0D;YAC1D,4DAA4D;YAC5D,qCAAqC;YACrC,OAAO,CAAC,CACN,CAAC,KAAK,IAAI;gBACV,CAAC,KAAK,SAAS;gBACf,CAAC,CAAC,KAAK,MAAM,IAAI,GAAG,CAAC;gBACrB,CAAC,CAAC,KAAK,UAAU,IAAI,GAAG,CAAC;gBACzB,CAAC,KAAK,QAAQ,CACf,CAAA;QACH,CAAC,CAAC,CACH,CACF,CAAA;IACH,CAAC;IAED,MAAM,CAAC,GAAY,EAAE,MAAc,CAAC;QAClC,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACtC,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,KAAK,aAAa,EAAE,CAAC;YACjC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAA;QAClB,CAAC;QAED,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;QAC9C,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;QACvD,MAAM,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,EAAE,UAAU,CAAC,CAAA;QACtD,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;QACrB,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;QACvB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QAEzB,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAA;QAC7D,IAAI,CAAC,OAAO;YACV,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAA;QACzD,IAAI,CAAC,OAAO;YACV,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,OAAO,CAAA;QACxD,IAAI,CAAC,OAAO;YACV,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,OAAO,CAAA;QACxD,IAAI,CAAC,OAAO;YACV,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAA;QAC1D,IAAI,CAAC,OAAO;YACV,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,CAAA;QACzD,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;QACzC,IAAI,CAAC,OAAO;YACV,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,OAAO,CAAA;QAC/D,GAAG,CAAC,KAAK,CAAC,eAAe,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAA;QACxC,IAAI,CAAC,OAAO;YACV,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,CAAA;QAC3D,IAAI,CAAC,OAAO;YACV,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,CAAA;QAC3D,IAAI,CAAC,OAAO;YACV,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,OAAO,CAAA;QAC3D,IAAI,CAAC,OAAO;YACV,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,OAAO,CAAA;QAC3D,IAAI,CAAC,OAAO;YACV,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,UAAU,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,OAAO,CAAA;QAC/D,IAAI,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,IAAI,CAAC,OAAO;gBACV,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,OAAO,CAAA;QAC1D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,OAAO;gBACV,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,OAAO,CAAA;YACxD,IAAI,CAAC,OAAO;gBACV,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,CAAA;YACzD,IAAI,CAAC,OAAO;gBACV,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,CAAA;QAC3D,CAAC;QAED,IAAI,GAAG,GAAG,CAAC,GAAG,IAAI,CAAA;QAClB,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;YACrC,GAAG,IAAI,GAAG,CAAC,CAAC,CAAW,CAAA;QACzB,CAAC;QAED,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3C,GAAG,IAAI,GAAG,CAAC,CAAC,CAAW,CAAA;QACzB,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,GAAG,CAAA;QAChB,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;QACxC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;QAEtB,OAAO,IAAI,CAAC,OAAO,CAAA;IACrB,CAAC;IAED,IAAI,IAAI;QACN,OAAO,CACL,IAAI,CAAC,KAAK,KAAK,aAAa,CAAC,CAAC;YAC5B,IAAI,CAAC,KAAK;YACZ,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAkB,CAAA;IAClD,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,KAAK,CAAA;IACnB,CAAC;IAED,IAAI,IAAI,CAAC,IAAmD;QAC1D,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAqB,CAAC,CAAC,CAAA;QACvD,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,aAAa,EAAE,CAAC;YAC3C,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;QAChB,CAAC;aAAM,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;QACnB,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,SAAS,CAAC,sBAAsB,GAAG,IAAI,CAAC,CAAA;QACpD,CAAC;IACH,CAAC;CACF;AA7OD,wBA6OC;AAED,MAAM,WAAW,GAAG,CAClB,CAAS,EACT,UAAkB,EACS,EAAE;IAC7B,MAAM,QAAQ,GAAG,GAAG,CAAA;IACpB,IAAI,EAAE,GAAG,CAAC,CAAA;IACV,IAAI,MAAM,GAAG,EAAE,CAAA;IACf,IAAI,GAAG,GAA0C,SAAS,CAAA;IAC1D,MAAM,IAAI,GAAG,iBAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,GAAG,CAAA;IAE5C,IAAI,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC;QACrC,GAAG,GAAG,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,CAAA;IAC3B,CAAC;SAAM,CAAC;QACN,oDAAoD;QACpD,MAAM,GAAG,iBAAU,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;QAC/B,EAAE,GAAG,iBAAU,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;QAE5B,GAAG,CAAC;YACF,IACE,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI,QAAQ;gBACjC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,UAAU,EACvC,CAAC;gBACD,YAAY;gBACZ,GAAG,GAAG,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,CAAA;YAC3B,CAAC;iBAAM,IACL,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,GAAG,QAAQ;gBAChC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,UAAU,EACvC,CAAC;gBACD,sDAAsD;gBACtD,GAAG,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;YACjD,CAAC;iBAAM,CAAC;gBACN,mCAAmC;gBACnC,EAAE,GAAG,iBAAU,CAAC,IAAI,CAAC,iBAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAA;gBACrD,MAAM,GAAG,iBAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;YACrC,CAAC;QACH,CAAC,QAAQ,MAAM,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAC;QAE9C,oDAAoD;QACpD,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,CAAA;QAC5C,CAAC;IACH,CAAC;IACD,OAAO,GAAG,CAAA;AACZ,CAAC,CAAA;AAED,MAAM,SAAS,GAAG,CAAC,GAAW,EAAE,GAAW,EAAE,IAAY,EAAE,EAAE,CAC3D,GAAG;KACA,QAAQ,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC;KACzB,QAAQ,CAAC,MAAM,CAAC;KAChB,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;AAExB,MAAM,OAAO,GAAG,CAAC,GAAW,EAAE,GAAW,EAAE,IAAY,EAAE,EAAE,CACzD,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAA;AAEtC,MAAM,SAAS,GAAG,CAAC,GAAY,EAAE,EAAE,CACjC,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAA;AAEtD,MAAM,SAAS,GAAG,CAAC,GAAW,EAAE,GAAW,EAAE,IAAY,EAAE,EAAE,CAC3D,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IACvB,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC;IAC5C,CAAC,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAA;AAElC,MAAM,QAAQ,GAAG,CAAC,KAAa,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;AAEtE,MAAM,cAAc,GAAG,CAAC,GAAW,EAAE,GAAW,EAAE,IAAY,EAAE,EAAE,CAChE,QAAQ,CACN,QAAQ,CACN,GAAG;KACA,QAAQ,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC;KACzB,QAAQ,CAAC,MAAM,CAAC;KAChB,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;KACpB,IAAI,EAAE,EACT,CAAC,CACF,CACF,CAAA;AAEH,kEAAkE;AAClE,MAAM,MAAM,GAAG;IACb,EAAE,EAAE,aAAa;IACjB,CAAC,EAAE,SAAS;CACb,CAAA;AAED,MAAM,SAAS,GAAG,CAChB,GAAW,EACX,GAAW,EACX,IAAY,EACZ,GAAY,EACZ,EAAE,CACF,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK;IACzB,CAAC,CAAC,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;QAC/B,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC;QAC1D,CAAC,CAAC,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,CAAA;AAEhD,MAAM,cAAc,GAAG,CACrB,GAAW,EACX,GAAW,EACX,IAAY,EACZ,GAAW,EACX,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,CAAA;AAE1D,MAAM,WAAW,GAAG,CAAC,GAAW,EAAE,IAAY,EAAE,EAAE,CAChD,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;AAE7C,MAAM,QAAQ,GAAG,CAAC,GAAW,EAAE,IAAY,EAAE,EAAE,CAC7C,CAAC,GAAG,CAAC,MAAM,KAAK,IAAI,GAAG,CAAC,CAAC,CAAC;IACxB,GAAG;IACL,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAA;AAElE,MAAM,OAAO,GAAG,CACd,GAAW,EACX,GAAW,EACX,IAAY,EACZ,IAAW,EACX,EAAE,CACF,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAC3B,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CACjD,CAAA;AAEH,8CAA8C;AAC9C,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AACvC,0DAA0D;AAC1D,MAAM,SAAS,GAAG,CAChB,GAAW,EACX,GAAW,EACX,IAAY,EACZ,GAAY,EACZ,EAAE,CACF,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAC1B,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC;IAC1C,GAAG,CAAC,MAAM,KAAK,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,CAC5D,CAAA", "sourcesContent": ["// parse a 512-byte header block to a data object, or vice-versa\n// encode returns `true` if a pax extended header is needed, because\n// the data could not be faithfully encoded in a simple header.\n// (Also, check header.needPax to see if it needs a pax header.)\n\nimport { posix as pathModule } from 'node:path'\nimport * as large from './large-numbers.js'\nimport type { EntryTypeCode, EntryTypeName } from './types.js'\nimport * as types from './types.js'\n\nexport type HeaderData = {\n  path?: string\n  mode?: number\n  uid?: number\n  gid?: number\n  size?: number\n  cksum?: number\n  type?: EntryTypeName | 'Unsupported'\n  linkpath?: string\n  uname?: string\n  gname?: string\n  devmaj?: number\n  devmin?: number\n  atime?: Date\n  ctime?: Date\n  mtime?: Date\n\n  // fields that are common in extended PAX headers, but not in the\n  // \"standard\" tar header block\n  charset?: string\n  comment?: string\n  dev?: number\n  ino?: number\n  nlink?: number\n}\n\nexport class Header implements HeaderData {\n  cksumValid: boolean = false\n  needPax: boolean = false\n  nullBlock: boolean = false\n\n  block?: Buffer\n  path?: string\n  mode?: number\n  uid?: number\n  gid?: number\n  size?: number\n  cksum?: number\n  #type: EntryTypeCode | 'Unsupported' = 'Unsupported'\n  linkpath?: string\n  uname?: string\n  gname?: string\n  devmaj: number = 0\n  devmin: number = 0\n  atime?: Date\n  ctime?: Date\n  mtime?: Date\n\n  charset?: string\n  comment?: string\n\n  constructor(\n    data?: Buffer | HeaderData,\n    off: number = 0,\n    ex?: HeaderData,\n    gex?: HeaderData,\n  ) {\n    if (Buffer.isBuffer(data)) {\n      this.decode(data, off || 0, ex, gex)\n    } else if (data) {\n      this.#slurp(data)\n    }\n  }\n\n  decode(\n    buf: Buffer,\n    off: number,\n    ex?: HeaderData,\n    gex?: HeaderData,\n  ) {\n    if (!off) {\n      off = 0\n    }\n\n    if (!buf || !(buf.length >= off + 512)) {\n      throw new Error('need 512 bytes for header')\n    }\n\n    this.path = decString(buf, off, 100)\n    this.mode = decNumber(buf, off + 100, 8)\n    this.uid = decNumber(buf, off + 108, 8)\n    this.gid = decNumber(buf, off + 116, 8)\n    this.size = decNumber(buf, off + 124, 12)\n    this.mtime = decDate(buf, off + 136, 12)\n    this.cksum = decNumber(buf, off + 148, 12)\n\n    // if we have extended or global extended headers, apply them now\n    // See https://github.com/npm/node-tar/pull/187\n    // Apply global before local, so it overrides\n    if (gex) this.#slurp(gex, true)\n    if (ex) this.#slurp(ex)\n\n    // old tar versions marked dirs as a file with a trailing /\n    const t = decString(buf, off + 156, 1)\n    if (types.isCode(t)) {\n      this.#type = t || '0'\n    }\n    if (this.#type === '0' && this.path.slice(-1) === '/') {\n      this.#type = '5'\n    }\n\n    // tar implementations sometimes incorrectly put the stat(dir).size\n    // as the size in the tarball, even though Directory entries are\n    // not able to have any body at all.  In the very rare chance that\n    // it actually DOES have a body, we weren't going to do anything with\n    // it anyway, and it'll just be a warning about an invalid header.\n    if (this.#type === '5') {\n      this.size = 0\n    }\n\n    this.linkpath = decString(buf, off + 157, 100)\n    if (\n      buf.subarray(off + 257, off + 265).toString() ===\n      'ustar\\u000000'\n    ) {\n      this.uname = decString(buf, off + 265, 32)\n      this.gname = decString(buf, off + 297, 32)\n      /* c8 ignore start */\n      this.devmaj = decNumber(buf, off + 329, 8) ?? 0\n      this.devmin = decNumber(buf, off + 337, 8) ?? 0\n      /* c8 ignore stop */\n      if (buf[off + 475] !== 0) {\n        // definitely a prefix, definitely >130 chars.\n        const prefix = decString(buf, off + 345, 155)\n        this.path = prefix + '/' + this.path\n      } else {\n        const prefix = decString(buf, off + 345, 130)\n        if (prefix) {\n          this.path = prefix + '/' + this.path\n        }\n        this.atime = decDate(buf, off + 476, 12)\n        this.ctime = decDate(buf, off + 488, 12)\n      }\n    }\n\n    let sum = 8 * 0x20\n    for (let i = off; i < off + 148; i++) {\n      sum += buf[i] as number\n    }\n\n    for (let i = off + 156; i < off + 512; i++) {\n      sum += buf[i] as number\n    }\n\n    this.cksumValid = sum === this.cksum\n    if (this.cksum === undefined && sum === 8 * 0x20) {\n      this.nullBlock = true\n    }\n  }\n\n  #slurp(ex: HeaderData, gex: boolean = false) {\n    Object.assign(\n      this,\n      Object.fromEntries(\n        Object.entries(ex).filter(([k, v]) => {\n          // we slurp in everything except for the path attribute in\n          // a global extended header, because that's weird. Also, any\n          // null/undefined values are ignored.\n          return !(\n            v === null ||\n            v === undefined ||\n            (k === 'path' && gex) ||\n            (k === 'linkpath' && gex) ||\n            k === 'global'\n          )\n        }),\n      ),\n    )\n  }\n\n  encode(buf?: Buffer, off: number = 0) {\n    if (!buf) {\n      buf = this.block = Buffer.alloc(512)\n    }\n\n    if (this.#type === 'Unsupported') {\n      this.#type = '0'\n    }\n\n    if (!(buf.length >= off + 512)) {\n      throw new Error('need 512 bytes for header')\n    }\n\n    const prefixSize = this.ctime || this.atime ? 130 : 155\n    const split = splitPrefix(this.path || '', prefixSize)\n    const path = split[0]\n    const prefix = split[1]\n    this.needPax = !!split[2]\n\n    this.needPax = encString(buf, off, 100, path) || this.needPax\n    this.needPax =\n      encNumber(buf, off + 100, 8, this.mode) || this.needPax\n    this.needPax =\n      encNumber(buf, off + 108, 8, this.uid) || this.needPax\n    this.needPax =\n      encNumber(buf, off + 116, 8, this.gid) || this.needPax\n    this.needPax =\n      encNumber(buf, off + 124, 12, this.size) || this.needPax\n    this.needPax =\n      encDate(buf, off + 136, 12, this.mtime) || this.needPax\n    buf[off + 156] = this.#type.charCodeAt(0)\n    this.needPax =\n      encString(buf, off + 157, 100, this.linkpath) || this.needPax\n    buf.write('ustar\\u000000', off + 257, 8)\n    this.needPax =\n      encString(buf, off + 265, 32, this.uname) || this.needPax\n    this.needPax =\n      encString(buf, off + 297, 32, this.gname) || this.needPax\n    this.needPax =\n      encNumber(buf, off + 329, 8, this.devmaj) || this.needPax\n    this.needPax =\n      encNumber(buf, off + 337, 8, this.devmin) || this.needPax\n    this.needPax =\n      encString(buf, off + 345, prefixSize, prefix) || this.needPax\n    if (buf[off + 475] !== 0) {\n      this.needPax =\n        encString(buf, off + 345, 155, prefix) || this.needPax\n    } else {\n      this.needPax =\n        encString(buf, off + 345, 130, prefix) || this.needPax\n      this.needPax =\n        encDate(buf, off + 476, 12, this.atime) || this.needPax\n      this.needPax =\n        encDate(buf, off + 488, 12, this.ctime) || this.needPax\n    }\n\n    let sum = 8 * 0x20\n    for (let i = off; i < off + 148; i++) {\n      sum += buf[i] as number\n    }\n\n    for (let i = off + 156; i < off + 512; i++) {\n      sum += buf[i] as number\n    }\n\n    this.cksum = sum\n    encNumber(buf, off + 148, 8, this.cksum)\n    this.cksumValid = true\n\n    return this.needPax\n  }\n\n  get type(): EntryTypeName {\n    return (\n      this.#type === 'Unsupported' ?\n        this.#type\n      : types.name.get(this.#type)) as EntryTypeName\n  }\n\n  get typeKey(): EntryTypeCode | 'Unsupported' {\n    return this.#type\n  }\n\n  set type(type: EntryTypeCode | EntryTypeName | 'Unsupported') {\n    const c = String(types.code.get(type as EntryTypeName))\n    if (types.isCode(c) || c === 'Unsupported') {\n      this.#type = c\n    } else if (types.isCode(type)) {\n      this.#type = type\n    } else {\n      throw new TypeError('invalid entry type: ' + type)\n    }\n  }\n}\n\nconst splitPrefix = (\n  p: string,\n  prefixSize: number,\n): [string, string, boolean] => {\n  const pathSize = 100\n  let pp = p\n  let prefix = ''\n  let ret: undefined | [string, string, boolean] = undefined\n  const root = pathModule.parse(p).root || '.'\n\n  if (Buffer.byteLength(pp) < pathSize) {\n    ret = [pp, prefix, false]\n  } else {\n    // first set prefix to the dir, and path to the base\n    prefix = pathModule.dirname(pp)\n    pp = pathModule.basename(pp)\n\n    do {\n      if (\n        Buffer.byteLength(pp) <= pathSize &&\n        Buffer.byteLength(prefix) <= prefixSize\n      ) {\n        // both fit!\n        ret = [pp, prefix, false]\n      } else if (\n        Buffer.byteLength(pp) > pathSize &&\n        Buffer.byteLength(prefix) <= prefixSize\n      ) {\n        // prefix fits in prefix, but path doesn't fit in path\n        ret = [pp.slice(0, pathSize - 1), prefix, true]\n      } else {\n        // make path take a bit from prefix\n        pp = pathModule.join(pathModule.basename(prefix), pp)\n        prefix = pathModule.dirname(prefix)\n      }\n    } while (prefix !== root && ret === undefined)\n\n    // at this point, found no resolution, just truncate\n    if (!ret) {\n      ret = [p.slice(0, pathSize - 1), '', true]\n    }\n  }\n  return ret\n}\n\nconst decString = (buf: Buffer, off: number, size: number) =>\n  buf\n    .subarray(off, off + size)\n    .toString('utf8')\n    .replace(/\\0.*/, '')\n\nconst decDate = (buf: Buffer, off: number, size: number) =>\n  numToDate(decNumber(buf, off, size))\n\nconst numToDate = (num?: number) =>\n  num === undefined ? undefined : new Date(num * 1000)\n\nconst decNumber = (buf: Buffer, off: number, size: number) =>\n  Number(buf[off]) & 0x80 ?\n    large.parse(buf.subarray(off, off + size))\n  : decSmallNumber(buf, off, size)\n\nconst nanUndef = (value: number) => (isNaN(value) ? undefined : value)\n\nconst decSmallNumber = (buf: Buffer, off: number, size: number) =>\n  nanUndef(\n    parseInt(\n      buf\n        .subarray(off, off + size)\n        .toString('utf8')\n        .replace(/\\0.*$/, '')\n        .trim(),\n      8,\n    ),\n  )\n\n// the maximum encodable as a null-terminated octal, by field size\nconst MAXNUM = {\n  12: 0o77777777777,\n  8: 0o7777777,\n}\n\nconst encNumber = (\n  buf: Buffer,\n  off: number,\n  size: 12 | 8,\n  num?: number,\n) =>\n  num === undefined ? false\n  : num > MAXNUM[size] || num < 0 ?\n    (large.encode(num, buf.subarray(off, off + size)), true)\n  : (encSmallNumber(buf, off, size, num), false)\n\nconst encSmallNumber = (\n  buf: Buffer,\n  off: number,\n  size: number,\n  num: number,\n) => buf.write(octalString(num, size), off, size, 'ascii')\n\nconst octalString = (num: number, size: number) =>\n  padOctal(Math.floor(num).toString(8), size)\n\nconst padOctal = (str: string, size: number) =>\n  (str.length === size - 1 ?\n    str\n  : new Array(size - str.length - 1).join('0') + str + ' ') + '\\0'\n\nconst encDate = (\n  buf: Buffer,\n  off: number,\n  size: 8 | 12,\n  date?: Date,\n) =>\n  date === undefined ? false : (\n    encNumber(buf, off, size, date.getTime() / 1000)\n  )\n\n// enough to fill the longest string we've got\nconst NULLS = new Array(156).join('\\0')\n// pad with nulls, return true if it's longer or non-ascii\nconst encString = (\n  buf: Buffer,\n  off: number,\n  size: number,\n  str?: string,\n) =>\n  str === undefined ? false : (\n    (buf.write(str + NULLS, off, size, 'utf8'),\n    str.length !== Buffer.byteLength(str) || str.length > size)\n  )\n"]}