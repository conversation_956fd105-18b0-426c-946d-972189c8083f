### **JobPlus V1.0 (MVP) - 前端开发计划 (静态数据版)**

**核心目标**：构建一个功能完整、交互流畅、视觉统一的前端应用。所有动态数据将由本地的 Mock 文件提供，所有“AI”功能将通过 `setTimeout` 和预设数据来模拟。

**AI (Cursor) 执行原则**:
1.  **逐个 Sprint 执行**：严格按照 Sprint 顺序完成任务。
2.  **文件路径精确**：严格按照指定的目录和文件名创建组件。
3.  **Mock 数据驱动**：所有需要动态数据的地方，都从指定的 `src/mock` 目录导入数据。
4.  **样式规范至上**：所有样式**必须**使用 Tailwind CSS 工具类，并遵循已制定的样式规范。
5.  **组件化**：优先拆分和复用组件。

---

### **Sprint 0: 项目奠基 (Project Foundation)**

**目标**: 搭建项目骨架，配置好所有工具链，建立起项目的基础布局和样式。这是保证后续所有工作一致性的关键。

**关键任务**:

1.  **初始化项目**:
    *   **Action for AI**: 使用 Vite 初始化一个 React + TypeScript 项目。
2.  **安装核心依赖**:
    *   **Action for AI**: `pnpm add tailwindcss postcss autoprefixer`
    *   **Action for AI**: `pnpm add @reduxjs/toolkit react-redux react-router-dom axios`
    *   **Action for AI**: `pnpm add lucide-react`
3.  **配置样式与组件库**:
    *   **Action for AI**: `npx tailwindcss init -p`
    *   **Action for AI**: 根据 **《样式规范 V1.0》**，完整填充 `tailwind.config.js` 和 `src/styles/globals.css`。
    *   **Action for AI**: `npx shadcn-ui@latest init` (按规范配置)。
    *   **Action for AI**: 添加基础组件: `npx shadcn-ui@latest add button card input label toast`.
4.  **创建基础布局**:
    *   **Action for AI**: 创建 `src/components/layout/Header.tsx` 和 `src/components/layout/MainLayout.tsx`。`MainLayout` 将包含 Header 和一个用于渲染子路由的 `Outlet`。

**产出代码示例 (`src/components/layout/MainLayout.tsx`):**

```typescript
// src/components/layout/MainLayout.tsx
import { Outlet } from "react-router-dom";
import Header from "./Header";

export function MainLayout() {
  return (
    <div className="flex flex-col min-h-screen bg-background">
      <Header />
      <main className="flex-grow container mx-auto px-4 py-8">
        <Outlet />
      </main>
    </div>
  );
}
```

---

### **Sprint 1: 用户认证流程 (Authentication Flow)**

**目标**: 构建用户登录和注册的完整静态页面和交互逻辑。

**Mock 数据**:

*   **Action for AI**: 创建 `src/mock/auth.ts`。
    ```typescript
    // src/mock/auth.ts
    export const mockUser = {
      id: 'user-123',
      username: '面试达人',
      email: '<EMAIL>',
    };
    ```

**关键任务**:

1.  **创建页面**:
    *   **Action for AI**: 创建 `src/pages/LoginPage.tsx`。
    *   **Action for AI**: 创建 `src/pages/RegisterPage.tsx`。
2.  **实现登录功能**:
    *   **Action for AI**: 在 `LoginPage.tsx` 中，使用 Shadcn 的 `Card`, `Input`, `Label`, `Button` 组件构建表单。
    *   **登录逻辑**: 点击“登录”按钮时，检查输入的邮箱和密码是否为 `<EMAIL>` 和 `password`。
        *   **成功**: 显示一个 `Toast` 提示“登录成功！”，然后使用 `useNavigate` 跳转到 `/dashboard`。
        *   **失败**: 显示一个 `destructive` 类型的 `Toast` 提示“邮箱或密码错误”。

**产出代码示例 (`src/pages/LoginPage.tsx`):**

```typescript
// src/pages/LoginPage.tsx
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { useNavigate } from "react-router-dom";

export function LoginPage() {
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, you'd get form values. Here we simulate.
    toast({ title: "登录成功！", description: "正在跳转到仪表盘..." });
    setTimeout(() => navigate('/dashboard'), 1000);
  };

  return (
    <div className="flex items-center justify-center min-h-[80vh]">
      <Card className="w-full max-w-sm">
        <CardHeader>
          <CardTitle className="text-2xl">登录 JobPlus</CardTitle>
          <CardDescription>输入您的凭据以访问您的账户。</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleLogin} className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="email">邮箱</Label>
              <Input id="email" type="email" placeholder="<EMAIL>" defaultValue="<EMAIL>" required />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="password">密码</Label>
              <Input id="password" type="password" defaultValue="password" required />
            </div>
            <Button type="submit" className="w-full mt-2">登录</Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
```

---

### **Sprint 2: 仪表盘与导航 (Dashboard & Navigation)**

**目标**: 创建用户登录后看到的第一个页面——仪表盘，提供核心操作入口和信息概览。

**Mock 数据**:

*   **Action for AI**: 创建 `src/mock/interviews.ts`。
    ```typescript
    // src/mock/interviews.ts
    export interface InterviewSummary {
      id: string;
      position: string;
      date: string;
    }
    export const mockInterviewHistory: InterviewSummary[] = [
      { id: 'int-1', position: 'Java后端工程师', date: '2023-10-26' },
      { id: 'int-2', position: '高级前端工程师', date: '2023-10-24' },
      { id: 'int-3', position: 'Java后端工程师', date: '2023-10-22' },
    ];
    ```

**关键任务**:

1.  **创建页面**:
    *   **Action for AI**: 创建 `src/pages/DashboardPage.tsx`。
2.  **实现组件**:
    *   **Action for AI**: 在 `DashboardPage.tsx` 中，放置一个醒目的主 CTA 按钮：“开始一场模拟面试”。
    *   **Action for AI**: 创建一个 `InterviewHistoryCard.tsx` 组件，用于显示单条面试记录。
    *   **Action for AI**: 在 `DashboardPage.tsx` 中，`map` `mockInterviewHistory` 数据，渲染出 `InterviewHistoryCard` 列表。

**产出代码示例 (`src/pages/DashboardPage.tsx`):**

```typescript
// src/pages/DashboardPage.tsx
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { mockInterviewHistory } from "@/mock/interviews";
import { PlusCircle, ArrowRight } from "lucide-react";
import { Link } from "react-router-dom";

export function DashboardPage() {
  return (
    <div className="space-y-8">
      <div className="bg-secondary p-8 rounded-lg text-center">
        <h1 className="text-3xl font-bold text-secondary-foreground mb-4">准备好迎接下一次面试了吗？</h1>
        <Button size="lg" asChild>
          <Link to="/interview/new">
            <PlusCircle className="mr-2 h-5 w-5" />
            开始一场模拟面试
          </Link>
        </Button>
      </div>

      <div>
        <h2 className="text-2xl font-semibold mb-4">最近的面试记录</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {mockInterviewHistory.map((item) => (
            <Card key={item.id}>
              <CardHeader>
                <CardTitle>{item.position}</CardTitle>
                <CardDescription>面试日期: {item.date}</CardDescription>
              </CardHeader>
              <CardContent>
                <Button variant="outline" className="w-full" asChild>
                  <Link to={`/history/${item.id}`}>
                    查看复盘报告 <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
```

---

### **Sprint 3: 简历中心 (Resume Center)**

**目标**: 构建简历上传（伪）、解析结果展示和 STAR 优化功能。

**Mock 数据**:

*   **Action for AI**: 创建 `src/mock/resume.ts`。
    ```typescript
    // src/mock/resume.ts
    export interface ResumeData {
      personalInfo: { name: string; email: string; phone: string; };
      workExperience: { id: string; company: string; role: string; description: string; }[];
      // ... other fields
    }
    export const mockResume: ResumeData = {
      personalInfo: { name: '面试达人', email: '<EMAIL>', phone: '138****8888' },
      workExperience: [
        { id: 'work-1', company: 'AI 未来公司', role: 'Java开发', description: '负责开发订单系统。' },
        { id: 'work-2', company: '数字科技', role: '后端工程师', description: '维护了一个支付网关。' }
      ],
    };
    ```

**关键任务**:

1.  **创建页面**:
    *   **Action for AI**: 创建 `src/pages/ResumeCenterPage.tsx`。
2.  **实现功能**:
    *   **上传 (伪)**: 创建一个区域，点击后触发文件选择框，但在选择文件后，仅用 `Toast` 显示“简历上传成功，正在解析...”，然后用 `setTimeout` 模拟解析延迟，最后使用 `mockResume` 数据填充表单。
    *   **表单展示**: 将 `mockResume` 数据动态渲染成一个可编辑的表单。
    *   **STAR 优化**: 在每个项目经历旁，放置一个“AI 优化”按钮。点击后，弹出一个 Modal（使用 Shadcn 的 `Dialog` 组件）。
    *   **优化逻辑 (伪)**: 在 Modal 中，用户可以输入 S/T/A/R，点击“一键生成”后，用 `setTimeout` 模拟 AI 生成，然后返回一段预设的、优化后的文本，如：“**[情境]** 在负责订单系统开发期间，**[任务]** 我面临着高并发场景下订单处理效率低下的挑战。**[行动]** 我通过引入缓存和异步消息队列，重构了核心下单逻辑。**[结果]** 最终将系统吞吐量提升了300%，订单处理时间从500ms降低至100ms。”

---

### **Sprint 4: AI 模拟面试 (Mock Interview)**

**目标**: 创建核心的模拟面试聊天界面。

**Mock 数据**:

*   **Action for AI**: 创建 `src/mock/chat.ts`。
    ```typescript
    // src/mock/chat.ts
    export interface Message {
      id: number;
      sender: 'AI' | 'USER';
      content: string;
    }
    export const aiResponses: string[] = [
      "好的，我看到了你的自我介绍。你在简历中提到了'AI 未来公司'的项目，能详细讲讲你在其中扮演的角色和主要贡献吗？",
      "听起来很有趣。关于你提到的订单系统，你们当时的技术选型是什么？为什么会选择这个方案？",
      "明白了。那我们来聊聊基础知识吧。你能解释一下 Java 的 JVM 内存模型吗？",
      "非常好。今天的面试就到这里吧，我们会在一周内通知你结果。你有什么想问我的吗？"
    ];
    ```

**关键任务**:

1.  **创建页面**:
    *   **Action for AI**: 创建 `src/pages/InterviewPage.tsx`。
2.  **创建组件**:
    *   **Action for AI**: 创建 `src/components/chat/MessageBubble.tsx`，用于展示单条消息，根据 `sender` 区分样式。
    *   **Action for AI**: 创建 `src/components/chat/ChatInput.tsx`，包含输入框和发送按钮。
3.  **实现聊天逻辑**:
    *   **Action for AI**: 在 `InterviewPage.tsx` 中，使用 `useState` 管理一个消息数组 `messages`。
    *   **初始化**: 页面加载时，`messages` 数组包含第一条 AI 的欢迎语。
    *   **发送消息**: 用户在 `ChatInput` 中输入内容并发送后，将用户的消息添加到 `messages` 数组。
    *   **接收消息 (伪)**: 紧接着，使用 `setTimeout` 模拟 AI 思考（约 1-2 秒），然后从 `aiResponses` 数组中按顺序取出下一条回复，并添加到 `messages` 数组。

---

### **Sprint 5: 整合与路由 (Integration & Routing)**

**目标**: 将所有页面串联起来，完成应用的主路由配置。

**关键任务**:

1.  **配置路由**:
    *   **Action for AI**: 在 `src/App.tsx` 中，使用 `react-router-dom` 设置所有路由。
        *   `/login` -> `LoginPage`
        *   `/register` -> `RegisterPage`
        *   `/` (根路径) -> 嵌套在 `MainLayout` 下
            *   `/dashboard` -> `DashboardPage`
            *   `/resume` -> `ResumeCenterPage`
            *   `/interview/new` -> `InterviewPage`
            *   `*` -> `NotFoundPage` (创建一个简单的 404 页面)
2.  **完善导航**:
    *   **Action for AI**: 在 `Header.tsx` 组件中，添加 `Link` 组件，使其可以导航到“仪表盘” (`/dashboard`) 和“简历中心” (`/resume`)。同时显示模拟的用户名，并提供一个“退出登录”按钮（点击后跳转回 `/login`）。

**产出代码示例 (`src/App.tsx`):**

```typescript
// src/App.tsx
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { MainLayout } from "./components/layout/MainLayout";
import { LoginPage } from "./pages/LoginPage";
import { DashboardPage } from "./pages/DashboardPage";
import { Toaster } from "@/components/ui/toaster"
// ... import other pages

function App() {
  return (
    <>
      <BrowserRouter>
        <Routes>
          <Route path="/login" element={<LoginPage />} />
          {/* ... other non-layout routes */}
          <Route element={<MainLayout />}>
            <Route path="/dashboard" element={<DashboardPage />} />
            {/* ... other layout routes */}
          </Route>
        </Routes>
      </BrowserRouter>
      <Toaster />
    </>
  );
}

export default App;
```

---

这个开发计划为 AI 提供了清晰、逐步的指令，每一步都有明确的目标、依赖的静态数据和可验证的产出。请开始执行 Sprint 0。

